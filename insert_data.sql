-- =============================================
-- Dify 数据库查询助手 - 测试数据插入脚本
-- =============================================

-- 使用数据库
USE dify_demo;

-- =============================================
-- 1. 插入用户数据 (customers)
-- =============================================
INSERT INTO customers (name, email, phone, address, status) VALUES
('张三', '<EMAIL>', '13800138001', '北京市朝阳区建国路1号', 'active'),
('李四', '<EMAIL>', '13800138002', '上海市浦东新区陆家嘴路2号', 'active'),
('王五', '<EMAIL>', '13800138003', '广州市天河区珠江路3号', 'active'),
('赵六', '<EMAIL>', '13800138004', '深圳市南山区科技路4号', 'inactive'),
('钱七', '<EMAIL>', '13800138005', '杭州市西湖区文三路5号', 'active'),
('孙八', '<EMAIL>', '13800138006', '成都市锦江区春熙路6号', 'active'),
('周九', '<EMAIL>', '13800138007', '武汉市武昌区中南路7号', 'suspended'),
('吴十', '<EMAIL>', '13800138008', '南京市鼓楼区中山路8号', 'active');

-- =============================================
-- 2. 插入供应商数据 (suppliers)
-- =============================================
INSERT INTO suppliers (supplier_name, contact_person, contact_phone, contact_email, address) VALUES
('科技数码供应商', '刘经理', '021-12345678', '<EMAIL>', '上海市徐汇区漕河泾开发区'),
('家居用品供应商', '陈经理', '010-87654321', '<EMAIL>', '北京市丰台区总部基地'),
('服装配饰供应商', '王经理', '020-11223344', '<EMAIL>', '广州市白云区服装城'),
('食品饮料供应商', '李经理', '0571-99887766', '<EMAIL>', '杭州市余杭区食品园区'),
('运动户外供应商', '张经理', '0755-55667788', '<EMAIL>', '深圳市宝安区体育用品城');

-- =============================================
-- 3. 插入商品数据 (products)
-- =============================================
INSERT INTO products (name, description, price, stock_quantity, supplier_id, category, status) VALUES
('iPhone 15 Pro', '苹果最新款智能手机，256GB存储', 8999.00, 50, 1, '数码电子', 'active'),
('MacBook Air M2', '苹果笔记本电脑，13英寸', 9999.00, 30, 1, '数码电子', 'active'),
('无线蓝牙耳机', '高品质音质，降噪功能', 299.00, 100, 1, '数码电子', 'active'),
('智能手表', '健康监测，运动追踪', 1299.00, 80, 1, '数码电子', 'active'),
('舒适沙发', '真皮材质，三人座', 3999.00, 20, 2, '家居用品', 'active'),
('实木餐桌', '橡木材质，可坐6人', 2599.00, 15, 2, '家居用品', 'active'),
('床上四件套', '纯棉材质，多种花色', 199.00, 200, 2, '家居用品', 'active'),
('时尚T恤', '纯棉材质，多种颜色', 89.00, 300, 3, '服装配饰', 'active'),
('牛仔裤', '弹性面料，修身版型', 199.00, 150, 3, '服装配饰', 'active'),
('运动鞋', '透气舒适，适合跑步', 399.00, 120, 5, '运动户外', 'active'),
('有机绿茶', '高山茶叶，清香淡雅', 128.00, 500, 4, '食品饮料', 'active'),
('坚果礼盒', '混合坚果，营养丰富', 88.00, 200, 4, '食品饮料', 'active');

-- =============================================
-- 4. 插入订单数据 (orders)
-- =============================================
INSERT INTO orders (customer_id, total_amount, order_status, shipping_address, payment_method) VALUES
(1, 9298.00, 'delivered', '北京市朝阳区建国路1号', 'alipay'),
(2, 3999.00, 'delivered', '上海市浦东新区陆家嘴路2号', 'wechat'),
(3, 687.00, 'shipped', '广州市天河区珠江路3号', 'credit_card'),
(1, 2599.00, 'confirmed', '北京市朝阳区建国路1号', 'alipay'),
(4, 1299.00, 'pending', '深圳市南山区科技路4号', 'wechat'),
(5, 216.00, 'delivered', '杭州市西湖区文三路5号', 'alipay'),
(6, 488.00, 'delivered', '成都市锦江区春熙路6号', 'wechat'),
(2, 199.00, 'cancelled', '上海市浦东新区陆家嘴路2号', 'credit_card'),
(8, 9999.00, 'shipped', '南京市鼓楼区中山路8号', 'alipay'),
(3, 399.00, 'delivered', '广州市天河区珠江路3号', 'wechat');

-- =============================================
-- 5. 插入客户反馈数据 (feedback)
-- =============================================
INSERT INTO feedback (customer_id, product_id, rating, comment, feedback_type) VALUES
(1, 1, 5, '手机性能很好，拍照效果出色，非常满意！', 'product'),
(1, 2, 4, '笔记本很轻薄，性能不错，就是价格有点贵', 'product'),
(2, 5, 5, '沙发质量很好，坐着很舒服，家人都很喜欢', 'product'),
(3, 8, 3, '衣服质量一般，洗了几次就有点变形了', 'product'),
(3, 9, 4, '裤子版型不错，就是颜色和图片有点差异', 'product'),
(5, 11, 5, '茶叶品质很好，包装也很精美，会回购', 'product'),
(5, 12, 4, '坚果很新鲜，口感不错，性价比高', 'product'),
(6, 10, 2, '鞋子穿了一周就开胶了，质量有问题', 'product'),
(8, 2, 5, '电脑运行很流畅，外观也很漂亮，推荐购买', 'product'),
(2, NULL, 1, '物流太慢了，客服态度也不好，很失望', 'service'),
(4, NULL, 3, '网站操作不够便捷，希望能改进用户体验', 'general'),
(6, NULL, 2, '配送员态度不好，包装也有破损', 'delivery');

-- =============================================
-- 验证数据插入结果
-- =============================================

-- 查看各表的数据统计
SELECT 'customers' as table_name, COUNT(*) as record_count FROM customers
UNION ALL
SELECT 'suppliers' as table_name, COUNT(*) as record_count FROM suppliers
UNION ALL
SELECT 'products' as table_name, COUNT(*) as record_count FROM products
UNION ALL
SELECT 'orders' as table_name, COUNT(*) as record_count FROM orders
UNION ALL
SELECT 'feedback' as table_name, COUNT(*) as record_count FROM feedback;

-- =============================================
-- 一些测试查询示例
-- =============================================

-- 查看所有用户信息
-- SELECT * FROM customers;

-- 查看所有商品信息
-- SELECT product_id, name, price, stock_quantity, category FROM products;

-- 查看差评用户（评分 <= 2）
-- SELECT DISTINCT c.name, c.email, f.rating, f.comment 
-- FROM customers c 
-- JOIN feedback f ON c.customer_id = f.customer_id 
-- WHERE f.rating <= 2;

-- 查看各供应商的商品数量
-- SELECT s.supplier_name, COUNT(p.product_id) as product_count
-- FROM suppliers s
-- LEFT JOIN products p ON s.supplier_id = p.supplier_id
-- GROUP BY s.supplier_id, s.supplier_name;

-- 查看订单状态统计
-- SELECT order_status, COUNT(*) as order_count, SUM(total_amount) as total_revenue
-- FROM orders
-- GROUP BY order_status;
