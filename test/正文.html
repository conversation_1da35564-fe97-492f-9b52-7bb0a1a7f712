<!DOCTYPE HTML PUBLIC '-//W3C//DTD HTML 4.01 Transitional//EN'><html><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8' /><style>.red_title{color: #ff0006;font-size: 30px;text-align: center;margin-top: 10px;font-family: "Microsoft YaHei";padding-bottom:40px}.baseinfo_table{width: 100%;border-collapse: collapse; border-spacing: 0 ;}.baseinfo_table td{border: 1px solid #d8d5d5;padding:10px;font-size: 13px;color: #8c8c8c;vertical-align: middle;height:36px}.baseinfo_table td.option_title{text-align: center;font-size: 14px;color: #34688f;width:147px;}  .option_title_width{width: 147px;}</style></head><center><div style="width:980px;" align="left"><br><table style="width:100%"><tbody><tr class="firstRow"><th><p class="title_p  panel-title" title="基本信息"><span class="title_icon"></span>基本信息<span class="fr w124 fa fa-angle-down"></span></p></th></tr><tr><td width="1425" valign="top">
<script type="text/javascript" src="/webrdp-gw/service/database/style.db/zyStyle/js/laydate.js"></script>
<script type="text/javascript" src="/webrdp-gw/_saas/_app/zyonline.app/common/uiSource.db/TimeObjectUtil.script"></script>
<script type="text/javascript" src="/webrdp-gw/_saas/_app/zyonline.app/common/fileres.db/fileres.script?v=1"></script>
<script type="text/javascript" src="/webrdp-gw/_saas/_app/zyonline.app/common/fileres.db/web-office-sdk.umd.script"></script>
<script type="text/javascript" src="/webrdp-gw/_saas/_app/zyonline.app/common/fileres.db/job.script"></script>
<script type="text/javascript" src="/webrdp-gw/_saas/_app/zyonline.app/common/fileres.db/operationWPS.script"></script>


<h1 class="red_title"><span class="readField" id="read_title">在线营销服务湖南分中心收文处理单</span></h1><p></p><table style="width:100%;table-layout:fixed;" class="baseinfo_table" data-sort="sortDisabled"> 
  <tbody> 
    <tr class="firstRow"> 
      <td colspan="1" rowspan="1" style="width: 12%; " class="option_title" valign="top">收文编号</td> 
      <td colspan="1" rowspan="1" style="word-break: break-all;width: 25%; " valign="top"><span class="readField" id="read_fileCorona">在线湘收</span>〔<span class="readField" id="read_fileAnnual">2025</span>〕<span class="readField" id="read_fileSerial">1089</span>号 
        <span class="readField" id="read_swFileNo" style="display:none">在线湘收〔2025〕1089号</span></td> 
      <td colspan="1" rowspan="1" style="width: 12%; " class="option_title" valign="top">登记人</td> 
      <td colspan="1" rowspan="1" valign="top"> 
        <span class="readField" id="read_swDrafter">湖南文书岗</span></td> 
      <td colspan="1" rowspan="1" style="width: 12%; " class="option_title" valign="top">收文日期</td> 
      <td colspan="1" rowspan="1" valign="top"> 
        <span class="readField" id="read_createDate">2025-07-28 08:37:07</span></td> 
    </tr> 
    <tr> 
      <td colspan="1" rowspan="1" style="word-break: break-all;" class="option_title" valign="top">来文字号</td> 
      <td colspan="1" rowspan="1" valign="top"> 
        <span class="readField" id="read_fileNo">移在线人力〔2025〕9号</span></td> 
      <td colspan="1" rowspan="1" style="word-break: break-all;" class="option_title" valign="top">缓急程度</td> 
      <td colspan="1" rowspan="1" valign="top"> 
        <span class="readField" id="read_urgencyLevel">一般</span></td> 
      <td colspan="1" rowspan="1" style="word-break: break-all;" class="option_title" valign="top">密级程度</td> 
      <td colspan="1" rowspan="1" valign="top"> 
        <span class="readField" id="read_secretLevel">无（密级）</span></td> 
    </tr> 
    <tr> 
      <td colspan="1" rowspan="1" style="word-break: break-all;" class="option_title" valign="top">来文单位</td> 
      <td colspan="1" rowspan="1" valign="top"> 
        <span class="readField" id="read_mainDept">人力资源部（党委组织部）</span></td> 
      <td colspan="1" rowspan="1" style="word-break: break-all;" class="option_title" valign="top">拟稿人</td> 
      <td colspan="1" rowspan="1" valign="top"> 
        <span class="readField" id="read_drafter" style="width:100%;">王腾</span></td> 
      <td colspan="1" rowspan="1" style="word-break: break-all;" class="option_title" valign="top">联系电话</td> 
      <td colspan="1" rowspan="1" valign="top"> 
        <span class="readField" id="read_tel">18810015358</span></td> 
    </tr> 
    <tr> 
      <td style="width: 100px; word-break: break-all;" class="option_title" width="217" valign="top">文件标题</td> 
      <td colspan="5" rowspan="1" style="word-break: break-all;" valign="top"> 
        <span class="readField" id="read_subject">2025年网络信息安全及反诈领域人才培养认证方案</span></td> 
    </tr> 
    <tr> 
      <td colspan="1" rowspan="1" style="word-break: break-all;" class="option_title" valign="top">主办部门</td> 
      <td colspan="5" rowspan="1" valign="top"> 
        <span class="readField" id="read_swMainSendCN" style="cursor:pointer;width:90%;float:left">湖南分中心-党群人力部（党风廉政办公室）</span> 
         
         
        </td> 
    </tr> 
    <tr> 
      <td colspan="1" rowspan="1" style="word-break: break-all;" class="option_title" valign="top">协办部门</td> 
      <td colspan="5" rowspan="1" valign="top"> 
        <span class="readField" id="read_swSubSendCN" style="cursor:pointer;width:90%;float:left"></span> 
         
         
        </td> 
    </tr> 
    <tr> 
      <td colspan="1" rowspan="1" style="word-break: break-all;" class="option_title" valign="top">分中心领导阅知</td> 
      <td colspan="5" rowspan="1" valign="top"> 
        <span class="readField" id="read_compleaderReadCN" style="cursor:pointer;width:90%;float:left"></span> 
         
        </td> 
    </tr> 
    <tr> 
      <td colspan="1" rowspan="1" class="option_title" valign="top">备注</td> 
      <td colspan="5" rowspan="1" valign="top"> 
        <span class="readField" id="read_remark"></span></td> 
    </tr> 
  </tbody> 
</table> 
<span class="readField" id="read_flowInfoList" style="display:none">领导批示,分中心领导意见,中心文书意见,主办部门领导意见,主办部门落实情况,协办部门领导意见,协办部门落实情况#公司总经理签发&amp;公司领导批示&amp;中心领导批示&amp;公司总经理审批&amp;公司分管业务领导审批&amp;公司业务分管领导审批&amp;公司采购分管领导审批&amp;公司领导阅知&amp;公司领导签发&amp;中心领导签发&amp;中心领导会签&amp;中心总经理签发&amp;中心领导处理&amp;中心领导审批,分中心领导批示&amp;分公司领导批示&amp;分公司领导阅知&amp;分中心领导阅知,分中心文书处理&amp;公司文书处理&amp;收文登记,主办部门领导审批,主办部门人员办理,协办部门领导审批,协办部门人员办理</span> 
<span class="readField" id="read_flowDecisionList" style="display:none"></span> 
 
 


<span class="readField" id="read_$$nendPreviewDocument" style="display:none">true</span>
<input type="hidden" name="drafter" value="王腾|wangteng3"><input type="hidden" name="tel" value="18810015358"><input type="hidden" name="subject" value="2025年网络信息安全及反诈领域人才培养认证方案"><input type="hidden" name="mainDept" value="人力资源部（党委组织部）"><input type="hidden" name="fileNo" value="移在线人力〔2025〕9号"><input type="hidden" name="createDate" value="2025-07-28 08:37:07"><input type="hidden" name="secretLevel" value="无（密级）"><input type="hidden" name="urgencyLevel" value="一般"><input type="hidden" name="swDrafter" value="湖南文书岗|hnwsg"><input type="hidden" name="swMainSendCN" value="湖南分中心-党群人力部（党风廉政办公室）"><input type="hidden" name="swSubSendCN" value=""><input type="hidden" name="compleaderReadCN" value=""><input type="hidden" name="fileCorona" value="在线湘收"><input type="hidden" name="fileAnnual" value="2025"><input type="hidden" name="fileSerial" value="1089"><input type="hidden" name="remark" value=""><input type="hidden" name="compleaderRead" value=""><input type="hidden" name="swMainSend" value="198ad811-ea3c-427a-8391-f0d04ea01d04#0b3f330e-ef43-4ee2-afcb-f1b5fe5dd10f#0008002600020000000000000000#-1#0067e578-0f73-44a6-bd0b-90d542e12d7a#wangqingyu"><input type="hidden" name="swSubSend" value=""><input type="hidden" name="swFileNo" value="在线湘收〔2025〕1089号"><input type="hidden" name="swMainSendID" value="35d769a5-1e72-4eec-8358-cb9c6b1c9d42"><input type="hidden" name="swSubSendID" value=""><input type="hidden" name="flowInfoList" value="领导批示,分中心领导意见,中心文书意见,主办部门领导意见,主办部门落实情况,协办部门领导意见,协办部门落实情况#公司总经理签发&amp;公司领导批示&amp;中心领导批示&amp;公司总经理审批&amp;公司分管业务领导审批&amp;公司业务分管领导审批&amp;公司采购分管领导审批&amp;公司领导阅知&amp;公司领导签发&amp;中心领导签发&amp;中心领导会签&amp;中心总经理签发&amp;中心领导处理&amp;中心领导审批,分中心领导批示&amp;分公司领导批示&amp;分公司领导阅知&amp;分中心领导阅知,分中心文书处理&amp;公司文书处理&amp;收文登记,主办部门领导审批,主办部门人员办理,协办部门领导审批,协办部门人员办理"><input type="hidden" name="flowDecisionList" value=""><input type="hidden" name="companyLeaderChecked" value=""><input type="hidden" name="$$fieldName_mobile" value="swFileNo,swDrafter,createDate,fileNo,urgencyLevel,secretLevel,mainDept,drafter,tel,subject,swMainSendCN,swSubSendCN,compleaderReadCN,remark,flowInfoList,flowDecisionList,$$nendPreviewDocument"><input type="hidden" name="InsidersCan" value="true"><input type="hidden" name="haveSendLeader" value=""><input type="hidden" name="$$nendPreviewDocument" value="true">












<input type="hidden" name="loadDataStatus" value="yes"><input type="hidden" name="filetype" value="公司收文"><input type="hidden" name="filetypeId" value="_DIDc4435693-1361-4a86-83a5-618ff91a1528"><input type="hidden" name="parentPiid" value="piid:b60a8ac6-53e1-47cb-a545-ab672ff779c4"><input type="hidden" name="company" value="湖南分中心"><input type="hidden" name="department" value="湖南分中心"><input type="hidden" name="wordKey" value="unique"><input type="hidden" name="jtFlag" value=""><input type="hidden" name="isUserDraft" value=""><input type="hidden" name="isSend" value=""><input type="hidden" name="holdAtt" value="yes"><input type="hidden" name="hasGz" value=""><input type="hidden" name="smartApproveCheked" value=""><p></p>
<input type="hidden" name="title" value="在线营销服务湖南分中心收文处理单"></td></tr></tbody></table><table style="width:100%"><tbody><tr class="firstRow"><th><p class="title_p panel-title" title="审批意见"><span class="title_icon"></span>意见区<span class="fr w124 fa fa-angle-down"></span><script>var buttonFun_W1666887640422= function(){var url = 'template_flowRecord_screen.page';
var piid = uniflow.piid;
var tiid = uniflow.tiid;
var dccWorkItems = document.getElementsByName('opinionScreenBtn')[0].dccWorkItems;
if (!dccWorkItems) {
  var dccWorkItems = $.wrdp.uniflow.record.getFlowRecordsAddRead(piid);
  dccWorkItems = uniflow.record.handleWorkItems(dccWorkItems);
  document.getElementsByName('opinionScreenBtn')[0].dccWorkItems = dccWorkItems;
}
var html =
  '<ul class="nav nav-tabs">' +
  '  <li name="screen" type="pre" class="active"><a>谁批给了我</a></li>' +
  (uniflow.getForm().get('sys_status') == '1' ? '' : '  <li name="screen" type="next"><a>我批给了谁</a></li>') +
  '  <li name="screen" type="siblings"><a>与我同时收到待办</a></li>' +
  '  <li name="screen" type="myFlow"><a>我的待办主流程</a></li>' +
  '  <li name="screen" type="deptFlow"><a>部门意见分类</a></li>' +
  '</ul>';
html +=
  '<div id="flowTraceDiv" style="height: 600px;">' + uniflow.record.get(url).parse(getFlowTrace('pre')) + '</div>';
var win = $.wrdp.window.open(
  {
    id: 'flowTrace',
    width: '1000',
    height: '700',
    template: 'Window-template-cancel-noheader-bootstrap.subform'
  },
  html,
  function () {}
);
$('[name="screen"]')
  .unbind('click')
  .bind('click', function () {
    $(this).addClass('active').siblings('li.active').removeClass('active');
    changeFlowTraceType($(this).attr('type'));
  });

function changeFlowTraceType(type) {
  var html = getFlowTrace(type);
  if (type != 'deptFlow') {
    html = uniflow.record.get(url).parse(html);
  }
  $('#flowTraceDiv').html(html);
}

function getFlowTrace(type) {
  var dcc = new wish.dao.Documents();
  switch (type) {
    case 'pre':
      var myWorkDoc = dccWorkItems.search('wiid', uniflow.tiid);
      var prewiid = myWorkDoc.get('prewiid');
      var preWorkDocTrace = dccWorkItems.searchDocument('wiid', prewiid);
      if (preWorkDocTrace) {
        dcc.add(preWorkDocTrace);
      }
      dcc.add(myWorkDoc);
      break;
    case 'next':
      var preWorkDccTrace = dccWorkItems.search('prewiid', uniflow.tiid);
      dcc.add(preWorkDccTrace);
      break;
    case 'siblings':
      var myWorkDoc = dccWorkItems.search('wiid', uniflow.tiid);
      var prewiid = myWorkDoc.get('prewiid');
      if (prewiid != '') {
        var preWorkDccTrace = dccWorkItems.search('prewiid', prewiid);
        dcc.add(preWorkDccTrace);
      }
      break;
    case 'myFlow':
      var mDcc = new wish.dao.Documents();
      var myWorkDoc = dccWorkItems.search('wiid', uniflow.tiid);
      mDcc.add(myWorkDoc);
      var prewiid = myWorkDoc.get('prewiid');
      while (prewiid != '') {
        var mDoc = dccWorkItems.search('wiid', prewiid);
        mDcc.add(mDoc);
        myWorkDoc = dccWorkItems.searchDocument('wiid', prewiid);
        if (myWorkDoc) {
          prewiid = myWorkDoc.get('prewiid');
        } else {
          prewiid = '';
        }
      }
      for (var i = mDcc.count(); i > 0; i--) {
        var mDoc = mDcc.get(i - 1);
        dcc.add(mDoc);
      }
      break;
    case 'deptFlow':
      dcc =
        '<iframe name="selectFlowIframe" id="selectFlowIframe" frameborder="0" style="width:100%;height:620px;padding:20px" src="' +
        wi.context +
        '/_saas/_app/zyonline.app/_system/_port/uniflow_zy.db/deptFlowTrace.page"></iframe>';
      break;
    default:
      break;
  }
  return dcc;
}
}</script></p></th></tr><tr><td width="1425" valign="top" class="uniflow-gloss-list-new"><style>
.glyphicon{
  margin-right:3px;
}

.notion{
    width: 700px;
    display: block;
    word-break: break-all;
    word-wrap: break-word;
    margin-bottom: 41px;
    font-size: 13px;
    line-height: 1.42857143;
    padding: 9.5px;
    margin: 0 0 10px;
    font-family: Menlo,Monaco,Consolas,"Courier New",monospace;
}
</style>
<table class="baseinfo_table table table-condensed" style="width:100%"><tbody><tr class="firstRow  "><td class="option_title" style="width: 200px;padding-bottom: 10px;padding-top: 10px;vertical-align: middle;"><span for="summary1">领导批示</span></td><td valign="top" class="uniflow-form-tableContent"><div style="width: 700px;word-break: break-all;font-size:12.0pt;" nodename="领导批示"><div class="f_big opStyle-0 notion" style="font-weight: bold; font-size: 14px; color: rgb(0, 0, 255); "><div>同意</div><div style="float:right;margin-bottom:0">赵峰(中心领导) 2025-07-27 14:54:19</div></div><div class="f_big opStyle-0 notion" style="font-weight: bold; font-size: 14px; color: rgb(0, 0, 255); "><div>请庆总先行审批。</div><div style="float:right;margin-bottom:0">赵峰(中心领导) 2025-07-08 20:45:45</div></div><div class="f_big opStyle-0 notion" style="font-weight: bold; font-size: 14px; color: rgb(0, 0, 255); "><div>同意</div><div style="float:right;margin-bottom:0">陈庆(中心领导) 2025-07-27 14:39:31</div></div><div class="f_big opStyle-0 notion" style="font-weight: bold; font-size: 14px; color: rgb(0, 0, 255); "><div>请平台运维中心会签</div><div style="float:right;margin-bottom:0">陈庆(中心领导) 2025-07-09 14:27:41</div></div></div></td></tr><tr class="firstRow  "><td class="option_title" style="width: 200px;padding-bottom: 10px;padding-top: 10px;vertical-align: middle;"><span for="summary1">分中心领导意见</span></td><td valign="top" class="uniflow-form-tableContent"><div style="width: 700px;word-break: break-all;font-size:12.0pt;" nodename="分中心领导意见"><div class="f_big opStyle-4 notion"><div>已阅。</div><div style="float:right;margin-bottom:0">谭献忠(湖南分中心) 2025-07-28 08:40:23</div></div><div class="f_big opStyle-4 notion"><div>已阅</div><div style="float:right;margin-bottom:0">王艳丽(湖南分中心) 2025-07-28 11:11:11</div></div><div class="f_big opStyle-4 notion"><div>请数智支撑中心阅办，请积极组织参加认证，安全岗需100%持证上岗。</div><div style="float:right;margin-bottom:0">孙薇嘉(湖南分中心) 2025-07-28 08:54:41</div></div></div></td></tr><tr class="firstRow  "><td class="option_title" style="width: 200px;padding-bottom: 10px;padding-top: 10px;vertical-align: middle;"><span for="summary1">中心文书意见</span></td><td valign="top" class="uniflow-form-tableContent"><div style="width: 700px;word-break: break-all;font-size:12.0pt;" nodename="中心文书意见"><div class="f_big opStyle-0 notion"><div>呈谭总阅示，王总、孙总阅；请党群人力部办理</div><div style="float:right;margin-bottom:0">湖南文书岗(湖南分中心-综合支撑部) 2025-07-28 08:38:22</div></div></div></td></tr><tr class="firstRow  "><td class="option_title" style="width: 200px;padding-bottom: 10px;padding-top: 10px;vertical-align: middle;"><span for="summary1">主办部门领导意见</span></td><td valign="top" class="uniflow-form-tableContent"><div style="width: 700px;word-break: break-all;font-size:12.0pt;" nodename="主办部门领导意见"><div class="f_big opStyle-5 notion"><div>请任鸣川组织相关人员及时参加认证</div><div style="float:right;margin-bottom:0">王清宇(湖南分中心-党群人力部（党风廉政办公室）) 2025-07-28 19:01:13</div></div></div></td></tr><tr class="firstRow  "><td class="option_title" style="width: 200px;padding-bottom: 10px;padding-top: 10px;vertical-align: middle;"><span for="summary1">主办部门落实情况</span></td><td valign="top" class="uniflow-form-tableContent"><div style="width: 700px;word-break: break-all;font-size:12.0pt;" nodename="主办部门落实情况"></div></td></tr><tr class="firstRow  "><td class="option_title" style="width: 200px;padding-bottom: 10px;padding-top: 10px;vertical-align: middle;"><span for="summary1">协办部门领导意见</span></td><td valign="top" class="uniflow-form-tableContent"><div style="width: 700px;word-break: break-all;font-size:12.0pt;" nodename="协办部门领导意见"></div></td></tr><tr class="firstRow  "><td class="option_title" style="width: 200px;padding-bottom: 10px;padding-top: 10px;vertical-align: middle;"><span for="summary1">协办部门落实情况</span></td><td valign="top" class="uniflow-form-tableContent"><div style="width: 700px;word-break: break-all;font-size:12.0pt;" nodename="协办部门落实情况"></div></td></tr></tbody></table></td></tr></tbody></table></div></center></html>