-- =============================================
-- Dify 数据库查询助手 - 数据库创建脚本
-- =============================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS dify_demo 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE dify_demo;

-- =============================================
-- 1. 用户表 (customers)
-- =============================================
CREATE TABLE customers (
    customer_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    email VARCHAR(150) UNIQUE NOT NULL COMMENT '邮箱地址',
    phone VARCHAR(20) COMMENT '电话号码',
    address TEXT COMMENT '地址',
    registration_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '用户状态'
) COMMENT '用户信息表';

-- =============================================
-- 2. 供应商表 (suppliers)
-- =============================================
CREATE TABLE suppliers (
    supplier_id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_name VARCHAR(100) NOT NULL COMMENT '供应商名称',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    address TEXT COMMENT '供应商地址',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '供应商信息表';

-- =============================================
-- 3. 商品表 (products)
-- =============================================
CREATE TABLE products (
    product_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    price DECIMAL(10,2) NOT NULL COMMENT '商品价格',
    stock_quantity INT DEFAULT 0 COMMENT '库存数量',
    supplier_id INT COMMENT '供应商ID',
    category VARCHAR(50) COMMENT '商品分类',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    status ENUM('active', 'inactive', 'discontinued') DEFAULT 'active' COMMENT '商品状态',
    FOREIGN KEY (supplier_id) REFERENCES suppliers(supplier_id) ON DELETE SET NULL
) COMMENT '商品信息表';

-- =============================================
-- 4. 订单表 (orders)
-- =============================================
CREATE TABLE orders (
    order_id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL COMMENT '客户ID',
    order_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '订单日期',
    total_amount DECIMAL(12,2) NOT NULL COMMENT '订单总金额',
    order_status ENUM('pending', 'confirmed', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending' COMMENT '订单状态',
    shipping_address TEXT COMMENT '收货地址',
    payment_method ENUM('credit_card', 'alipay', 'wechat', 'cash') COMMENT '支付方式',
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE CASCADE
) COMMENT '订单信息表';

-- =============================================
-- 5. 客户反馈表 (feedback)
-- =============================================
CREATE TABLE feedback (
    feedback_id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL COMMENT '客户ID',
    product_id INT COMMENT '商品ID（可选）',
    rating INT CHECK (rating >= 1 AND rating <= 5) COMMENT '评分(1-5分)',
    comment TEXT COMMENT '反馈内容',
    feedback_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '反馈时间',
    feedback_type ENUM('product', 'service', 'delivery', 'general') DEFAULT 'general' COMMENT '反馈类型',
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE SET NULL
) COMMENT '客户反馈表';

-- =============================================
-- 创建索引以提高查询性能
-- =============================================

-- 用户表索引
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_status ON customers(status);

-- 商品表索引
CREATE INDEX idx_products_supplier ON products(supplier_id);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_status ON products(status);

-- 订单表索引
CREATE INDEX idx_orders_customer ON orders(customer_id);
CREATE INDEX idx_orders_date ON orders(order_date);
CREATE INDEX idx_orders_status ON orders(order_status);

-- 反馈表索引
CREATE INDEX idx_feedback_customer ON feedback(customer_id);
CREATE INDEX idx_feedback_product ON feedback(product_id);
CREATE INDEX idx_feedback_rating ON feedback(rating);
CREATE INDEX idx_feedback_date ON feedback(feedback_date);

-- =============================================
-- 显示创建的表结构
-- =============================================
SHOW TABLES;

-- 显示表结构信息
DESCRIBE customers;
DESCRIBE suppliers;
DESCRIBE products;
DESCRIBE orders;
DESCRIBE feedback;
