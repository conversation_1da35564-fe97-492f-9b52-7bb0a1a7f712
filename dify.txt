好，那么下面呢我们就来再说后续的这几个应用啊，一个是chart flow,还有一个workflow.对吧我们来看看这两种就是进阶相关的一些内容。刚才说了，agent它有可能对工具调用不是那么太为精准。那么这个时候我们就可以自己去构建工作流来去完成这样的完成这样的什么呢？就是嗯完成更复杂的，符合用户意愿的这样的这样的智能体。好吧？好，那么首先呢第一个差的flow,我们来给大家去演示一个案例啊。呃，差的flow呢在这儿吧，我们叫做一个啊医疗二g医疗系统，构建啊二一二g医疗系统的一个二g的这种问答吧。好吧，那我们怎么做呢？这个工作流里面它这些东西都叫做节点，跟你所有见到的那种可视化的这种AI工具，其实是一样的，都有这样的节点啊，那么cos里面有很多节点，对不对？这些节点用起来本身不会太难，大家琢磨琢磨看一看这个名字大概就会用，明白吗？那我们现在要做一个什么事儿呢？就是我有一个文档，这个文档是这样子的，是这样的啊，一个内科学的文档，我把它打开给大家看一下啊。呃，python用的什么版本啊，大家有问题可以挑出来，没关系啊，换个线，换个线应该也能搜出来。我不换了啊，太卡了还是吧？太卡了，并不是我这儿搜不出来，能搜出来就是有点卡。好吧，我就不给大家演示这个太卡的东西了。Python用什么版本呢？呃呃，我的python应该应该应该没有用到python吧。这里面现在就我现在没有用到python啊，但是它底层的python用什么版本我还真没看啊啊。那你说未来我们要开发这种呃agent也好，workflow也好，python用什么版本呢？建议大家用三点幺零以上的，明白吗？可能用三点幺三的，现在比较多promote,是不是也是嵌入的一种方式？哎，没错啊，你看SQW同学来哦，我说一下啊，就是如果这效果果好，效果不好，就是你的智能体或者是工作流，它的效果不好的话，你要从以下几个方面去解决。第一个方面就是你的promote可能起始词不好，明白吗？这是一个角度。另外一个角度呢就是知识库的问题。知识库可能不好，或者说那个知识库对应的嵌入模型不好，或者知识库里面设置那些参数不合理，明白吧？第三个，如果再不精准啊，那么还有一个终极解决的方案就是去微调模型了。微调模型。第二，第三种完全是工具搞不定的，它可能会涉及到代码的开发。我表达清楚了吗？啊，我表达清楚了嘛，对吧？提示词其实有限制的啊，就是你可能设置的话，它没有想象的那么特别，就是特别复杂的话，反而也不是太好啊也不是太好。因为你模型要理解这些内容，它也可以看成一个嵌入的内容，就给模型做一个角色的定义啊。好，来我们看这个内容啊，反正有一些科学的这个名词吧。现在呢我要在这个模型里面啊，待会儿呢我构建的这个RG啊，做一件事儿啊，就是用户提问之后，他要从这个知识库里面来搜索内容给我作答，明白了吗？所以我们首先要构建这么一个知识库，对不对？好，那么我创建知识库在这里创建，然后我们选择这个文件。刚刚我给大家给大家看的这个文件啊，就内科科好吧，就内科学点下一步。然后在这儿呢，我什么也不管了，我这儿选一个大的模型，然后混合检索我这里top十，好吧，咱们吸取教训啊，多一些好，等待它完成这个知识库的构建之后呢，我们就可以去呃使用了。现在正在嵌入处理啊，正在处理中啊，其实也应该很快啊，等待一会儿我们在这儿就开始先操作吧。呃，我们要做这件事儿的话，大家想想应该怎么去做啊。首先呢用户提问肯定是system query,就是它在这个节点里面提问了之后，你要去搜索什么？是不是搜索那个知识库里面知识库里面内容啊，所以这儿要加一个知识检索，明白吗？要加一个知识检索。好，知识检索里面检索什么东西呢？我们选择知识呃，这个选择知识检索在这儿选择知识库，就是那个内科学里面的内容。我们把它选过来输出呢，也就是说它根据用户的输入自由动嵌入之后，那那么去检索你的知识库里面内容，把符合的那些相似的片段来交由谁去处理呢？交由你的大模型去处理。所以我们往后再跟上一个大模型的这样的一个节点，LM的节点。这大模型的节点的作用就是来将知识库里面的内容啊来结合用户的提问来给我作答出去。所以这个上下文啊，只要前面有知识库，一般往往我们这个上下文就设置为知识库就行了，明白吗？那这里有一个系统提示词，系统提示词我把它复制一下啊，把系统提示词拿过来，就告诉他你是一个什么呢？你是一个精通内科学的高手，使用以下内容作为学习的知识放在这里面标签里面了啊，建议大家在提示词里面多加这种标签，它比较准确，明白吗？好，那么回答用户的问题啊，那用户的问题在哪里呢？我们用户的问题要提问过来，它得结合用户的问题，结合返回的匹配的内容才能给你作答嘛。所以在这里要设置一下用户的提问啊，user这儿加一下OK.那呃完事儿之后呢，它能返回过来，结果我们就直接回复这个RG的，非常简单，对吧？非常简单啊，那么好直接回复我们这儿应该选的就是大模型。看到没有？它是LM的一个tax LM的一个text啊，就是它直接作答。那我们现在看看这个知识库有没有构建完，构建完了对吧？构建完了，那我们现在是不是就可以提问了，来测试啊呃先发布吧，待会儿别没了啊，发布一下，咱们再预览。我在这里预览来给大家写一下，比如说什么是啊问他一下啊，我看一下这里面内容，比如说AHR是什么啊，AHR什么是AHR,看他能不能结合知识库能给我作答出来啊，它正在运行啊，正在运行啊。就按理说我们啊已经这个设置了嵌入的模型，还有top k啊比较高，它回答应该是比较准确的啊，来看一下啊什么气道高反应性是吧？什么什么变应原理啊，什么理化因素啊，什么运动啊是吧？啊，理化因素啊，什么运动就是这玩意儿，对吧？他答的应该是引入了这个知识库里面内容啊是没有问题的。 Ok吧，那你是不是就可以考虑我能不能将这个知识库的内容换成你自己企业里面的知识是不是也可以做这件事啊，对不对？那当然这个知识啊我说了，嗯，我们用这样的工作流，它能控制一步一步怎么去做啊啊如果你这个知识里面的内容，这只是用当前啊这个工具，就define这个工具提供的知识库。如果你的知识库内容特别大，那就得用到向量数据库啊。像啊那个post gren里面是不是也有向量化的东西，还有REDIS还还有什么呢？现在比IOM大模型里面这个领域里面比较火的一个叫啊murls是吧？是不是这么一个呃向量数据库，对吧？那么它支持的量级也非常大啊。好，呃，那么你的知识库也可以是外部的在define里面，或者呢我们这种效果。如果不好的话，那就得涉及到大家可能自己去开发这样的工作流。用python的这种语言，每一个环节自己去设计一些参数来去调节了啊，或者这个大模型。你如果不用外部的，就是用企业内部的啊企业内部的那有一些工具它是支持配置的，有一些工具根本没法支持配置。比如说cos没法用外部的模型，那怎么办呢？你只能是自己去开发了，就没法用这样的工具了，明白吧？就是简单来理解呢，用工具还是受限。如果自己去开发这样的AI的这种工种流也好，智能体也好啊，就是涉及到编写代码就比较灵活了。那现在呢往往这种agent的或者是work flow的开发啊，所谓的AI应用的开发，其实就是这一块内容啊，并不是说用工具，用工具也是啊包含在内的啊工具的能力嘛。好，系统提示词怎么弄的？这个系统提示词呢是呃我自己提前准备。好的，大家也可以点那个小心心，让它自己给你生成。比如说我们来看一下啊，我点它，我就告诉他我要做什么内容啊，它是结合知识库。哎，这个中文呢我看一下啊，结合知识库内容啊，回复用户问题，只能从知识库中获取答案。好，注意看啊，然后呢我我这里啊你看我我写的很平白呃就是很平民的这么一个提示词。现在呢我可以让它给我生成其给用dep c define面提提供了这样工工具啊，让它给我生成种种示词词。个人觉觉得好好提示示词生成成实是词里面生成的啊，就大家用cococos的话，你会发现cos用起来非常爽，它的提示词非常爽啊，甚至它把提示词是不是单独了一个单独的功能模块拿出来啊，给给大家伙儿去用啊。那define里面这个提示词也还行是吧？大概的样子看起来也还可以啊，我觉得它不如这个cos里面生成的更更更有吸引力啊。当然你得改啊，你不能说它生成什么，我用什么，你得看看符，不符合你的需求，我这儿就不用它了啊，如果点应用就替换了，好吧，就替换掉了啊。我这儿就不应用了，还是用我原来的，这是我前面准备。好的，明白了吗？啊，query怎么知道这些参数变量的呢？ Text system query啊，这个东西他怎么知道？那肯定是你开发这个工具的人去设置的，在底层代码里面去设置的呢。这肯定不是我们现在设置的，他肯定是节点之间。比如说通过连线连连接之后，他就知道上游里面有什么样的参数，对不对啊？这个肯定是开发这个工具的人去设置的啊，那你说我们我们用这个工具，它就啊其实你不需要关注这些，明白吗？你关注他怎么去用就行了。你但是你要开发这样的平台，你被关注这样的问题，OK吧。好，这个是咱们给大家说的呃，这个工作流。哎，我这有没有解释清楚，大家还没有问题来说一下啊，还有没有问题，没有问题我就下一个了啊。再来给大家去演示一个数据查询的一个功能。好吧，我再来给大家演示一个案例，一个是数据可视化的，一个是数据库里面查询数据的。你们想看哪一个来告诉我告诉我各位，我看你们还活着吗？啊，还还有没有睡着了啊，数据库的是吧？好，OK.那么啊那么我们给大家演示一下数据库的好吧，数据库的是这样的啊，我本机呢有一个数据库，我给您看一下，这这有个nakitty给他瞅一眼啊。哎，各位你们看我的电脑呃，不是看我说话这个声音卡不卡，我想了解一下到底这个问题是哪啊，后面得解决一下这个问题，不我不知道是不是这个工具的问题，直播工具的问题啊，就是你们听我说话卡吗？我现在电脑是巨卡，但我的电脑性能应该是ok的啊。哦，抖音DB不是他不是抖音，是my DB,就我这里面的同学们啊啊不卡是吧？提示词多了之后，如何精准找到a点的那个呃GESOA那个同学说的提示词多了，如何精准找到a点的那个你提示词多了啊，我觉得啊嗯太多或者太少，都不足以说特别精准的去找到对应的工具啊。你的起始词呢呃就是中等之后，你可以去明确的提示，你要用什么样的工具，明白吗？当然肯定有一些你是呃就是工具太多了，你你肯定在提示词里面可能不完，不能完全的写写写出来啊，就算你能完全的写出来，有可能这个提示词太多了，它对模型这个角色的预示预设啊，对吧？它可能上一文有有可能超了，对吧？啊，你可能限制你设置不了太多，那就算你设置的太多，那其实也有可能会对大模型有影响，明白吗？有影响，所以呢我们才最好用工作流不卡是吧？不卡就行了啊啊，那么现在呢我有一些工作呃，就是数据库里面的一些表吧。我来给大家看一下这个数据表啊，这个数据表呢它里面内容是这个样子的。来，我给大家打开看一下啊，呃简单来理解一下啊，认识一下它里面有个用户表啊，里面有一些数据，有一些商品表，它里面也有一些数据，还有一些订单表，有一些数据，还有订单对应的细节的表啊，有一些数据，还有一些什么供应商的表，就是商品供应商表，有一些数据，还有什供供应商关系，供应商关系就是啊呃产品产品和供应商的一个关系啊啊有一些数据，还有呢就是客户反馈的一些信息等等等等吧。这样的数据啊，数据库里面的每个表里面的数据，就是是现在大家看到的这个数据。还有想通过一个事儿，就有通过自然语言的方式啊，自然语言的方式用户数问啊用户提问了。那么经过一个工作流，就是或者叫我们的这这个AI应用，然后直接给我返回出来啊，这种表格数据表格也好，或者数据这个自然语言的方式结果也好，来给我给我返回过来。比如说我问你啊，我的产品对应的供应商有哪些啊，告诉我啊打了差评的用户有哪些啊，直接用自然语言的方式来反馈出来。所以这个AI应用你怎么去构建呢？就涉及到啊那么用户的提问啊，我得理解之后转换成底层的circle.然后这个circle呢是不是才从数据库里面来给我们去查询数据啊，来做这样的事儿，对不对？好，大家听明白这个需求了吗？听明白的给我敲个一，那下面我就要实现这个功能了啊，实现这个功能呢，你首先需要将这些表的结构来给它作为知识库来给它创建出来。好，所以呢我在这儿创建一下啊，我把它发布一下啊，咱们开始啊。好，来到这里创建一个空白应用，我们这儿还是差t flow吧，差t flow啊。当工作流我待会儿告诉它和叉t flow什么区别啊，这边呢就是数据查询助手，好吧，创建一下啊，我们得首先创建一个知识库，刚才说的是吧？来到这里创建知识库。那我把刚刚我把刚刚这个文本给它上传一下，这个table infour就是刚才的那个schema,好吧，把schema上传，咱们也选择推荐吧，其他我就不不啊不改了啊，就这样了。好，那么来到这里呢，我们就开始实现一下啊，怎么去实现。首先用户呢在这里肯定是自然语言的方式来提问。这个就是system a query就是用户提问的内容，明白吗？好，那么下面呢大家想想我们要跟什么？是不是你用户提问了之后，你肯定是要查询比如说差评的用户啊，那商品有哪些等等等等这样的语言呗。那你肯定是从知识库里面就给我检索内容了，所以呢把知识库给它设置过来。这个知识库就是刚刚table info里面内容啊，我们拿过来放在底下吧，拿到拿过来知识库。好，那么知识库检索过来之后，下一步是不是要跟大语言模型干嘛结合我的知识来给我生成circle了吧，是不是让它给我生成circle键？好，所以呢在这儿我们让大约模型，我得给他找一个起始词来告他啊。前面这是他的上下文，我要用，他要让他通过上下文来给我做一件事儿，就是给我生成circle查询数据库的circle.这个提示词也是我自己写的啊，没有优化啊，大家也可以优化优化。那没有优化，我觉得呃也还好，现在用的啊就是告诉他你是一个circle专家，擅长呢根据自然语言啊结合知识库这里面内容上下文的内容引用了啊呃生成circl语句做了一些限制啊，就是不能查底层的表，只能查slide这种操作，对吧？然后呢，如果不能转换呢，你就给我生成slide什么呃，就是哎这好像有问题啊，slide无法生成circle from端看到没有啊，就是生成这样的语句啊。好，这个是system系统提示词，我们还要提呃提供一下用户啊，你输入的那个内容是不是用户的那个内容，我们也要传进来，他才能结合知识库里面内容来给你生成正确的irircle啊，这就完事儿了啊啊那么它生成的是circle,我们可以改名字，它生成的这个色果告诉大家伙啊，它生成这个circle,它是以呃返回的时候，是以mark down那种格式来给你返回的。Slide什么什么东西？巴拉巴拉巴拉是这种格式啊，所以呢这个时候呢我们只想他要它里面的circle,所以这个时候我们还要跟一个东西啊，就大元模型，你说它生成的是纯circle吗？你虽然做了一些提示词的限制，但是它生成的circle有可能是mark down这种格式没法在数据库里面直接执行嘛。所以这个时候呢，我们还要继续的干嘛呢？继续给它加一个这样的，哎，我把它拉过来啊，我们要继续加一个参数提取的方式，呃，参数提取的节点，来把对应的circle正确的circle给它提取出来。所以在这儿呢有一个参数提取，看到没有？直接放到底下啊，参数提取要干嘛呢？我们针对输入的变量就是这个大于生成circle这个LM生成的这个结果。我们要提取一个什么东西呢？提取一个circle,这个描述很重很很很啊需要啊准确一些啊，就是我们从结果中提取的sql语句啊，可以直接执行的circle.好，那么现在我们就提取到了一个circle,它会把这个circle的变量给它传递出去啊，传递出去。那下一步有了这个circle,我们是不是要连接数据库进行查询了？那怎么去连接数据库呢？所以后面还要跟一个节点这个节点跟上的话，就是得查询你的真正的数据库的这种查询节点了。但这个节点它没有直接查询数据库的节点啊，那么我们就得通过这种代码的方式来去实现。就代码执行。好，那我这儿就搞一个代码执行。这个代码执行的作用就是拿着你这里传过来的circle,我呢要真正的去连接mcsql数据库来去执行这个circle就circl语句来返回结果了，明白吗？做这个事儿啊。好，这个代码执行它里面的内容，我来给大家去填一下啊，稍等一下，我把它拿过来，直接粘过来啊，粘过来。好吧，我已经粘过来了，看到没有？那它里面有一个参数啊，这个参数呢是叫做啊在这儿这个参数是慢方法，里面的参数是不是叫circle啊？所以我这儿需要写一个circle,这个circle来自的来来来来自于哪里呢？是不是参数提取器里面提取的circle,我们把参数提取器里面的circle给它设置过来，那它就会传给这个方法。这个circle它去连接了啊我的呃我的本机的数据库，这个幺零四就是我本机啊。刚才给大家看的那个数据库，连接上之后呢，它给我执行，执行，最后返回了一个什么呢？Reout看到没有？叫这里返回了reout啊，结果会返回。如果你不放心，可以先测试一下，我们来测试一下试一试啊。比如说色拉星from我们来查询一个表里面的数据啊，就这个表吧，叫customers,看能不能连接数据库，把这个结果给我们查出来啊。好，运行，大家可以看到是不是它能返回这个结果啊，这个结果是这个样子的啊，是这种reout,什么巴拉巴拉一堆比较难看啊，比较难看，但是它确实是结果。那待会儿呢，如果你用户提问之后，他会给你生成circle,生成circle呢，让你提取了circle,是不是代码执行给你能把这个结果查询出来。但它是内容，刚才看的比较不友好的格式啊，所以我们怎么办呢？我们再给它加一个大语言模型的节点，让它整理数据的格式，好吧，整理数据的格式。这个大语言模型呢，它的作用就是针对你前面代码执行的节点的内容来给我呃整理格式来回复啊。所以呢在这儿我们设定一下，它这是整理格式啊，整理数据格式好吧，那在这样的上下文呢嗯上下文设置谁呢？设置其实不要上下文行不行？嗯，这个上下文应该是代码执行里面的结果吧，把代码执行的结果拿过来。好吧，那我这儿就告诉他，比如说结合用户的提问哦，我复制一下。好，我们拿过来不敲了啊，太慢了。结合用户的提问。当然用户的提问在哪里呢？在这里这个就是用户的提问，system curry交易用的，请问以及该问题的结果，在这里就是上下文的内容。这就结果来给用户做出更好的回复，不需要反问啊，直接回复就行了。好，这是整理格式。那整理了格式之后啊，直接回复的结果，我们来设置一下，就是把这个直接回复的内容来给它呃生成circle.不是它啊，我们要把谁拿过来呢？就是整理数据格式这个大语言模型，它的他的结果给他拿出来，是不是这样，我们这个工作流就构建完了，对不对？构建完了啊。好，一般生产环境基于性能原因禁止创建啊外建是吧？呃，那么这样AI怎么知道各个表如何关联呢？啊，那就没法知道了，或者呢你就不要拿scammer了，你需要描述性的语言来去告诉他，就是准备这样的知识库的文。呃，这个内容明白吗？明白吗？这个WALTER同学就以你肯定不能是基于现在我已有的这样的内容来去扒拉丢到知识库了，不能像我一样了，你得描述一下，你得自己去描述了，明白吗？为啥不用circle直接查询数据库呢？反倒是中间多了好多节点。呃，我说了啊，它没有数据库，直接执行circle,这个这个节点明白吗？所以我们才加了这些节点，它没有这样的节点。如果有这样的节点，那不就好了吗啊？那你说我为什么不用circle直接查询数据库呢？你直接用circle查询数据库，他可能对你估这个IT人员比较友好。但是对于我现在要创建的这种啊人员使用的话，可能不友好，我不懂circle,对吧？那我根本就不知道怎么去写circle.好，那怎么办呢？我们我们可以开发这样的智能体给他们去用嘛，对吧？那我们测试一下，好吧啊，预览我们来测试啊，比如说找出打了差评的用户。呃，我们看这个结果啊，我告诉大家，它有可能生成的也不对。但这个不对的话，你第一个可以用提示词去再重新修改了。第二个呢，要给出更详细的表之间的关联结构。要提醒他明白吗？重新构建你的知识库去也有可能呃那么就是哪个环节出问题，你去调节哪个地方吧？是这样的啊，咱们不可能保证说一次性的全都准确，也也也不是太可能啊，但我这个结果是正确的啊，我们可以点开这个工作流，看一看它生成的circle你就明白了，它一定是从我这里面生成的circle输出的circle看到没有？ Slede tion from customer join另外一个用户返回表就是customer,还有一个用户的feedback, feedback就是这个表，这个表在哪儿呢？用户的反馈表在这儿feedback看到没有？就是用户啊，他打了差评，他默认定义的是评分是低于三分还是几分的，低于两分的是差评的用户，那低于两分的你看是不是谁呀？低两分的呃，评分是谁啊？这是product ID什么comment? Comment是反馈内容，这个是reading是评分，一二三第三个字段啊，一二三第三个字段。那打了二分的呃，是不是有它呀，对吧？小于二的那只有这一条了，一了一是谁呢？那custom ID是一的customm, ID是一的那肯定是那stom ID为一的，cut ID为一的。哎，我我还真找不到了啊，customer ID啊，这怎么是客户ID没有了呢？哦，客户ID成了张三，我就不对了啊，反正这个是查的应该是对的，我就不执行这个circle了。好吧，我不看了，少于等于二的，小于等于二的应该是一个顺期啊，应该是对的。我记得啊小于等于二的应该是两个啊，是不是我看错了，我就不执行了。好吧，那我们这样吧，我们再让他查询一下，简单的一个，比如说找出所有的供应商啊，找出所有的产品嘛，找出啊那么找出所有的产品有哪些，我们看能不能找出来产品呢？是这些产品啊，a到勾好吧，a到勾。呃，今天讲的这些我看啊呃今天讲的这些circle和提示词啥的都能给我们吗？可以给大家。就是我说了，今天晚上啊你跟完这个课程，老师呢会把这个DSL文件我会发给你，明白吗？就就我会会完全的导出来，我的啥其实词里面是什么内容我都会给你好吧。多的节点就是为了解释客户的提问和给出合适的格式反馈给用户。哎，这个一曲高歌同学说的没错啊。好，你看他是a道勾是都有的啊，价格是多少，库存是多少？哎，这个这还是OK的啊，他肯定也有circle了。这个他不是瞎意淫写出来的，对不对？他肯定有circle啊，那这个社会在哪儿呢？代码执行呃，参数提取器这个提取的circle看到没有？Slide product ID name,什么deaticpreprice巴拉巴拉from products.就从这里面查的嘛，就从它这里面查的，对不对？好，OK吧，那这个结果呢就就长那个样子啊，应该应该是没啥问题啊，好吧。呃，然后呢这是给大家演示的工作流啊，就是workfloor啊差的flow.