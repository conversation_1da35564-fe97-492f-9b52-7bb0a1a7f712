# 数据库表结构

## 1. 用户表 (customers)

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| customer_id | INT | PRIMARY KEY, AUTO_INCREMENT | 用户唯一标识 |
| name | VARCHAR(100) | NOT NULL | 用户姓名 |
| email | VARCHAR(150) | UNIQUE, NOT NULL | 邮箱地址 |
| phone | VARCHAR(20) | - | 电话号码 |
| address | TEXT | - | 用户地址 |
| registration_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 注册时间 |
| status | ENUM('active', 'inactive', 'suspended') | DEFAULT 'active' | 用户状态 |

## 2. 供应商表 (suppliers)

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| supplier_id | INT | PRIMARY KEY, AUTO_INCREMENT | 供应商唯一标识 |
| supplier_name | VARCHAR(100) | NOT NULL | 供应商名称 |
| contact_person | VARCHAR(50) | - | 联系人姓名 |
| contact_phone | VARCHAR(20) | - | 联系电话 |
| contact_email | VARCHAR(100) | - | 联系邮箱 |
| address | TEXT | - | 供应商地址 |
| created_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

## 3. 商品表 (products)

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| product_id | INT | PRIMARY KEY, AUTO_INCREMENT | 商品唯一标识 |
| name | VARCHAR(100) | NOT NULL | 商品名称 |
| description | TEXT | - | 商品描述 |
| price | DECIMAL(10,2) | NOT NULL | 商品价格 |
| stock_quantity | INT | DEFAULT 0 | 库存数量 |
| supplier_id | INT | FOREIGN KEY | 供应商ID |
| category | VARCHAR(50) | - | 商品分类 |
| created_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| status | ENUM('active', 'inactive', 'discontinued') | DEFAULT 'active' | 商品状态 |

## 4. 订单表 (orders)

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| order_id | INT | PRIMARY KEY, AUTO_INCREMENT | 订单唯一标识 |
| customer_id | INT | NOT NULL, FOREIGN KEY | 客户ID |
| order_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 订单日期 |
| total_amount | DECIMAL(12,2) | NOT NULL | 订单总金额 |
| order_status | ENUM('pending', 'confirmed', 'shipped', 'delivered', 'cancelled') | DEFAULT 'pending' | 订单状态 |
| shipping_address | TEXT | - | 收货地址 |
| payment_method | ENUM('credit_card', 'alipay', 'wechat', 'cash') | - | 支付方式 |

## 5. 客户反馈表 (feedback)

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| feedback_id | INT | PRIMARY KEY, AUTO_INCREMENT | 反馈唯一标识 |
| customer_id | INT | NOT NULL, FOREIGN KEY | 客户ID |
| product_id | INT | FOREIGN KEY | 商品ID（可选） |
| rating | INT | CHECK (rating >= 1 AND rating <= 5) | 评分(1-5分) |
| comment | TEXT | - | 反馈内容 |
| feedback_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 反馈时间 |
| feedback_type | ENUM('product', 'service', 'delivery', 'general') | DEFAULT 'general' | 反馈类型 |


