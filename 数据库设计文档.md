# Dify 数据库查询助手 - 数据库设计文档

## 概述

本文档描述了 Dify 数据库查询助手项目的数据库设计，包含完整的表结构、字段说明、索引设计和关系定义。

## 数据库配置

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS dify_demo 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE dify_demo;
```

**数据库特性**：
- **字符集**: utf8mb4 - 支持完整的 Unicode 字符集，包括 emoji
- **排序规则**: utf8mb4_unicode_ci - 支持多语言排序和比较

## 数据表设计

### 1. 用户表 (customers)

存储系统用户的基本信息。

```sql
CREATE TABLE customers (
    customer_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    email VARCHAR(150) UNIQUE NOT NULL COMMENT '邮箱地址',
    phone VARCHAR(20) COMMENT '电话号码',
    address TEXT COMMENT '地址',
    registration_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '用户状态'
) COMMENT '用户信息表';
```

**字段说明**：

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| customer_id | INT | PRIMARY KEY, AUTO_INCREMENT | 用户唯一标识 |
| name | VARCHAR(100) | NOT NULL | 用户姓名 |
| email | VARCHAR(150) | UNIQUE, NOT NULL | 邮箱地址，唯一约束 |
| phone | VARCHAR(20) | - | 电话号码 |
| address | TEXT | - | 用户地址 |
| registration_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 注册时间 |
| status | ENUM | DEFAULT 'active' | 用户状态：active/inactive/suspended |

### 2. 供应商表 (suppliers)

存储商品供应商的信息。

```sql
CREATE TABLE suppliers (
    supplier_id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_name VARCHAR(100) NOT NULL COMMENT '供应商名称',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    address TEXT COMMENT '供应商地址',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '供应商信息表';
```

**字段说明**：

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| supplier_id | INT | PRIMARY KEY, AUTO_INCREMENT | 供应商唯一标识 |
| supplier_name | VARCHAR(100) | NOT NULL | 供应商名称 |
| contact_person | VARCHAR(50) | - | 联系人姓名 |
| contact_phone | VARCHAR(20) | - | 联系电话 |
| contact_email | VARCHAR(100) | - | 联系邮箱 |
| address | TEXT | - | 供应商地址 |
| created_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

### 3. 商品表 (products)

存储商品的详细信息。

```sql
CREATE TABLE products (
    product_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    price DECIMAL(10,2) NOT NULL COMMENT '商品价格',
    stock_quantity INT DEFAULT 0 COMMENT '库存数量',
    supplier_id INT COMMENT '供应商ID',
    category VARCHAR(50) COMMENT '商品分类',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    status ENUM('active', 'inactive', 'discontinued') DEFAULT 'active' COMMENT '商品状态',
    FOREIGN KEY (supplier_id) REFERENCES suppliers(supplier_id) ON DELETE SET NULL
) COMMENT '商品信息表';
```

**字段说明**：

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| product_id | INT | PRIMARY KEY, AUTO_INCREMENT | 商品唯一标识 |
| name | VARCHAR(100) | NOT NULL | 商品名称 |
| description | TEXT | - | 商品详细描述 |
| price | DECIMAL(10,2) | NOT NULL | 商品价格，精确到分 |
| stock_quantity | INT | DEFAULT 0 | 库存数量 |
| supplier_id | INT | FOREIGN KEY | 供应商ID，外键关联 |
| category | VARCHAR(50) | - | 商品分类 |
| created_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| status | ENUM | DEFAULT 'active' | 商品状态：active/inactive/discontinued |

### 4. 订单表 (orders)

存储客户订单信息。

```sql
CREATE TABLE orders (
    order_id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL COMMENT '客户ID',
    order_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '订单日期',
    total_amount DECIMAL(12,2) NOT NULL COMMENT '订单总金额',
    order_status ENUM('pending', 'confirmed', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending' COMMENT '订单状态',
    shipping_address TEXT COMMENT '收货地址',
    payment_method ENUM('credit_card', 'alipay', 'wechat', 'cash') COMMENT '支付方式',
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE CASCADE
) COMMENT '订单信息表';
```

**字段说明**：

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| order_id | INT | PRIMARY KEY, AUTO_INCREMENT | 订单唯一标识 |
| customer_id | INT | NOT NULL, FOREIGN KEY | 客户ID，外键关联 |
| order_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 订单创建日期 |
| total_amount | DECIMAL(12,2) | NOT NULL | 订单总金额 |
| order_status | ENUM | DEFAULT 'pending' | 订单状态：pending/confirmed/shipped/delivered/cancelled |
| shipping_address | TEXT | - | 收货地址 |
| payment_method | ENUM | - | 支付方式：credit_card/alipay/wechat/cash |

### 5. 客户反馈表 (feedback)

存储客户对商品和服务的反馈信息。

```sql
CREATE TABLE feedback (
    feedback_id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL COMMENT '客户ID',
    product_id INT COMMENT '商品ID（可选）',
    rating INT CHECK (rating >= 1 AND rating <= 5) COMMENT '评分(1-5分)',
    comment TEXT COMMENT '反馈内容',
    feedback_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '反馈时间',
    feedback_type ENUM('product', 'service', 'delivery', 'general') DEFAULT 'general' COMMENT '反馈类型',
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE SET NULL
) COMMENT '客户反馈表';
```

**字段说明**：

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| feedback_id | INT | PRIMARY KEY, AUTO_INCREMENT | 反馈唯一标识 |
| customer_id | INT | NOT NULL, FOREIGN KEY | 客户ID，外键关联 |
| product_id | INT | FOREIGN KEY | 商品ID，外键关联（可选） |
| rating | INT | CHECK (1-5) | 评分，1-5分 |
| comment | TEXT | - | 反馈内容 |
| feedback_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 反馈时间 |
| feedback_type | ENUM | DEFAULT 'general' | 反馈类型：product/service/delivery/general |

## 数据库关系图

```
customers (1) ----< (N) orders
customers (1) ----< (N) feedback
suppliers (1) ----< (N) products
products (1) ----< (N) feedback
```

**关系说明**：
- 一个客户可以有多个订单（1:N）
- 一个客户可以提交多个反馈（1:N）
- 一个供应商可以提供多个商品（1:N）
- 一个商品可以收到多个反馈（1:N）

## 索引设计

为了提高查询性能，创建了以下索引：

### 用户表索引
```sql
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_status ON customers(status);
```

### 商品表索引
```sql
CREATE INDEX idx_products_supplier ON products(supplier_id);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_status ON products(status);
```

### 订单表索引
```sql
CREATE INDEX idx_orders_customer ON orders(customer_id);
CREATE INDEX idx_orders_date ON orders(order_date);
CREATE INDEX idx_orders_status ON orders(order_status);
```

### 反馈表索引
```sql
CREATE INDEX idx_feedback_customer ON feedback(customer_id);
CREATE INDEX idx_feedback_product ON feedback(product_id);
CREATE INDEX idx_feedback_rating ON feedback(rating);
CREATE INDEX idx_feedback_date ON feedback(feedback_date);
```

## 常用查询场景

### 1. 查找差评用户
```sql
SELECT DISTINCT c.name, c.email, f.rating, f.comment 
FROM customers c 
JOIN feedback f ON c.customer_id = f.customer_id 
WHERE f.rating <= 2;
```

### 2. 查看所有商品信息
```sql
SELECT product_id, name, price, stock_quantity, category 
FROM products 
WHERE status = 'active';
```

### 3. 查看商品对应的供应商
```sql
SELECT p.name as product_name, s.supplier_name 
FROM products p 
JOIN suppliers s ON p.supplier_id = s.supplier_id;
```

### 4. 订单状态统计
```sql
SELECT order_status, COUNT(*) as order_count, SUM(total_amount) as total_revenue
FROM orders
GROUP BY order_status;
```

## 数据完整性约束

1. **外键约束**：
   - products.supplier_id → suppliers.supplier_id
   - orders.customer_id → customers.customer_id
   - feedback.customer_id → customers.customer_id
   - feedback.product_id → products.product_id

2. **检查约束**：
   - feedback.rating 必须在 1-5 之间

3. **唯一约束**：
   - customers.email 必须唯一

4. **非空约束**：
   - 关键业务字段设置为 NOT NULL

## 设计特点

1. **可扩展性**：表结构设计考虑了未来功能扩展的需要
2. **数据完整性**：通过外键约束保证数据一致性
3. **查询优化**：合理的索引设计提高查询性能
4. **业务友好**：字段命名和注释清晰，便于理解和维护
5. **国际化支持**：使用 utf8mb4 字符集支持多语言数据
