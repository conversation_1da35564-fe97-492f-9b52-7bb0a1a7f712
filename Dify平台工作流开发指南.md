# Dify 平台工作流开发指南

## 概述

本指南重点介绍 Chat Flow 和 Workflow 两种进阶应用类型的开发方法。

### Chat Flow vs Workflow

- **Chat Flow**: 适用于对话式交互场景，支持复杂的对话流程控制
- **Workflow**: 适用于自动化任务处理，通过节点连接实现复杂的业务逻辑

相比传统的 Agent 应用，工作流能够提供更精确的控制，避免工具调用不准确的问题。

## 案例一：医疗问答系统（RAG 实现）

### 系统概述

构建一个基于内科学知识库的问答系统，用户可以通过自然语言提问，系统从知识库中检索相关信息并生成准确回答。

### 实现步骤

#### 1. 知识库创建

1. **准备文档**: 上传内科学相关的文档资料
2. **配置参数**:
   - 选择大型嵌入模型
   - 设置混合检索模式
   - Top-K 设置为 10（提高召回率）

#### 2. 工作流设计

工作流包含以下节点：

```
用户提问 → 知识检索 → 大语言模型 → 回复结果
```

#### 3. 节点配置详解

**知识检索节点**:
- 输入：用户查询（System Query）
- 功能：根据用户输入检索知识库中的相关片段
- 输出：匹配的知识片段

**大语言模型节点**:
- 上下文：知识库检索结果
- 系统提示词示例：
```
你是一个精通内科学的专家。请使用以下知识库内容作为参考：
<knowledge>
{context}
</knowledge>

请根据用户的问题，结合上述知识内容给出准确的回答。
用户问题：{query}
```

#### 4. 测试验证

测试查询示例：
- "什么是 AHR？"
- 系统能够准确识别并解释"气道高反应性"的概念

### 优化建议

1. **提示词优化**: 使用标签结构提高模型理解准确性
2. **知识库质量**: 确保文档内容的专业性和完整性
3. **参数调优**: 根据实际效果调整 Top-K 值和检索策略

## 案例二：数据库查询助手

### 系统概述

构建一个自然语言转 SQL 的查询助手，用户可以用自然语言提问，系统自动生成并执行 SQL 查询，返回格式化的结果。

### 工作流设计

```
用户提问 → 知识检索 → SQL生成 → 参数提取 → 代码执行 → 格式整理 → 返回结果
```

#### 节点配置详解

**1. 知识检索节点**
- 输入：用户的自然语言查询
- 知识库：数据库表结构信息（Schema）
- 输出：相关的表结构信息

**2. SQL 生成节点（大语言模型）**
- 系统提示词：
```
你是一个 SQL 专家，擅长根据自然语言和数据库结构生成 SQL 查询语句。

限制条件：
1. 只能执行 SELECT 查询操作
2. 不允许执行 INSERT、UPDATE、DELETE 等修改操作
3. 如果无法生成有效的 SQL，请返回：SELECT '无法生成有效查询' as message

用户问题：{query}
数据库结构：{context}
```

**3. 参数提取节点**
- 功能：从 LLM 生成的 Markdown 格式结果中提取纯 SQL 语句
- 提取目标：可直接执行的 SQL 语句

**4. 代码执行节点**


**5. 格式整理节点（大语言模型）**
- 功能：将查询结果格式化为用户友好的回答
- 系统提示词：
```
请根据用户的问题和查询结果，生成清晰易懂的回答。
不需要反问，直接提供结果即可。

用户问题：{query}
查询结果：{context}
```

### 测试示例

**查询示例 1**: "找出打了差评的用户"
- 生成 SQL: `SELECT * FROM customers c JOIN feedback f ON c.customer_id = f.customer_id WHERE f.rating <= 2`
- 返回结果：评分低于等于 2 分的用户信息

**查询示例 2**: "找出所有的产品有哪些"
- 生成 SQL: `SELECT product_id, name, description, price FROM products`
- 返回结果：所有产品的详细信息列表


## 常见问题解答


### Q: 工作流和传统开发的区别？

A: 
- **工具开发**: 受限于平台功能，但开发效率高
- **代码开发**: 更灵活，可以实现复杂的自定义逻辑
- **选择建议**: 简单场景使用工具，复杂场景考虑代码开发

### Q: 如何处理大规模知识库？

A: 
1. 使用专业的向量数据库（如 Milvus、PostgreSQL with pgvector）
2. 优化文档分块策略
3. 实施分层检索机制
4. 考虑知识图谱等结构化存储方式
