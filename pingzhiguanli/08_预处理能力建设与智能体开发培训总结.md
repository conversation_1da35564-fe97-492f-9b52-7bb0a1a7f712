# 预处理能力建设与智能体开发培训总结

主要涵盖预处理能力建设工作、智能体开发实践、业务与技术协作以及跨部门沟通改进等内容。

## 1. 工作职责概述

### 1.1 预处理能力建设

#### 1.1.1 主要职责
- **预处理能力建设**：负责预处理相关的能力建设工作
- **流程体验**：负责流程体验相关工作
- **多渠道覆盖**：涉及自助热线、IVR、中国移动APP等不同渠道

#### 1.1.2 工作特点
- **渠道多样化**：整个体系区分很多渠道
- **实际导向**：根据实际情况开展工作
- **系统性强**：涉及多个系统和平台的协调

### 1.2 需求拟定和系统开发

#### 1.2.1 需求管理流程
1. **需求拟定**：根据业务需要拟定系统需求
2. **需求提交**：将需求文档提交给数字团队
3. **评估开发**：数字团队评估开发可行性
4. **需求对接**：与技术团队进行需求对接
5. **应用推广**：系统上线后的应用推广工作

#### 1.2.2 典型项目案例
- **预处理费用质疑系统**：已完成的系统开发项目
- **费用智能体**：正在开发的智能体项目
- **流程穿测**：定期进行的流程体验和问题发现工作

## 2. 智能体开发实践

### 2.1 费用智能体开发案例

#### 2.1.1 项目背景
- **应用对象**：面向客服代表的生产型智能体
- **功能定位**：处理客户费用账户问题
- **发展规划**：从小智能体逐步扩展到大智能体

#### 2.1.2 开发策略
- **分1步实施**：先做小的子智能体，再串成大智能体
- **性能考虑**：避免运行速度过慢影响生产环境使用
- **父子关系**：采用父子关系架构，先做子关系部分

#### 2.1.3 功能扩展规划
- **当前功能**：费用账户问题处理
- **未来扩展**：
  - 账单分析功能
  - 订购业务分析功能
  - 综合智能体整合

### 2.2 灵运平台使用

#### 2.2.1 平台访问路径
- **入口位置**：OA系统 → 常用系统 → 灵云平台
- **创建方式**：本省应用 → 本省创建
- **协作模式**：与数字团队成员协作搭建

#### 2.2.2 平台特点
- **全网应用**：全网31个省份都可使用
- **创新性**：生产型智能体在全网范围内较少
- **自主探索**：缺乏外省经验借鉴，需要自主摸索

#### 2.2.3 工作流管理
- **查看功能**：可以查看智能体的工作流情况
- **参数选择**：支持参数选择和运行测试
- **接口调用**：显示调用的接口信息
- **验证测试**：支持工作流验证和测试

### 2.3 提示词优化工作

#### 2.3.1 工作内容
- **提示词编写**：根据工作流输出参数编写提示词
- **大模型应用**：利用大模型分析并输出期望结果
- **持续优化**：根据测试结果不断优化提示词

#### 2.3.2 技术要求
- **工作流理解**：需要理解工作流的分支和逻辑
- **参数分析**：分析入参和出参的含义和用途
- **结果验证**：验证输出结果是否符合业务需求

#### 2.3.3 挑战和困难
- **技术门槛**：需要一定的技术基础才能理解工作流
- **业务理解**：技术人员需要深入理解业务需求
- **沟通成本**：业务与技术之间的沟通成本较高

## 3. 业务与技术协作

### 3.1 协作模式和挑战

#### 3.1.1 协作现状
- **分工明确**：业务侧负责需求，技术侧负责实现
- **知识壁垒**：业务不懂技术，技术不懂业务
- **沟通困难**：存在专业术语和理解差异

#### 3.1.2 主要挑战
- **理解差异**：业务人员难以理解技术实现细节
- **表达困难**：技术人员难以用业务语言表达技术方案
- **效率问题**：沟通不畅导致开发效率降低

### 3.2 沟通问题分析

#### 3.2.1 典型问题表现
- **技术术语过多**：技术人员使用过多专业术语
- **业务需求不明确**：业务人员难以准确表达技术需求
- **方案理解困难**：业务人员难以理解技术方案的含义

#### 3.2.2 沟通障碍原因
- **知识背景差异**：双方的知识结构和背景不同
- **表达方式不同**：技术思维与业务思维的表达方式差异
- **缺乏共同语言**：缺乏双方都能理解的沟通语言

## 4. 具体案例研究

### 4.1 外省号码处理案例

#### 4.1.1 问题描述
- **原始输出**：该号码系统内无有效资料，请核对是否输入正确
- **期望输出**：该号码系统内无有效资料，请核对该号码是否为本省移动号码
- **业务需求**：让客服代表更清楚地了解问题原因

#### 4.1.2 沟通过程
1. **需求提出**：业务人员提出修改输出内容的需求
2. **技术回应**：技术人员解释接口返回内容的技术限制
3. **理解困难**：业务人员无法理解技术术语和实现方式
4. **方案提供**：技术人员提供三种解决方案
5. **选择困难**：业务人员无法理解方案差异
6. **电话沟通**：最终通过电话沟通解决问题

#### 4.1.3 解决方案
- **方案一**：透传省公司输出（不可修改）
- **方案二**：对非正常值进行二次转换（最终采用）
- **方案三**：提供枚举值进行转化（业务侧无法提供）

### 4.2 沟通改进启示

#### 4.2.1 问题根源
- **专业术语障碍**：技术人员使用过多专业术语
- **方案解释不清**：未能用业务语言解释技术方案
- **需求理解偏差**：对业务需求的理解存在偏差

#### 4.2.2 改进方向
- **接地气沟通**：使用更贴近业务的语言进行沟通
- **结果导向**：关注业务需求的最终结果而非技术细节
- **方案简化**：简化技术方案的表达方式

## 5. 跨部门沟通改进

### 5.1 沟通障碍分析

#### 5.1.1 知识壁垒
- **业务知识**：技术人员缺乏深入的业务理解
- **技术知识**：业务人员缺乏必要的技术基础
- **领域差异**：不同专业领域的思维方式差异

#### 5.1.2 表达方式差异
- **技术表达**：偏重技术实现细节和专业术语
- **业务表达**：偏重业务结果和用户体验
- **理解偏差**：双方对同一问题的理解角度不同

### 5.2 解决方案探讨

#### 5.2.1 轮岗制度
- **业务理解**：技术人员通过轮岗了解业务需求
- **技术认知**：业务人员通过学习提升技术认知
- **共同语言**：建立双方都能理解的沟通语言

#### 5.2.2 沟通机制优化
- **需求澄清**：在项目开始前充分澄清业务需求
- **方案评估**：技术人员评估需求的合理性和可行性
- **结果确认**：确保最终结果符合业务期望

### 5.3 协作建议

#### 5.3.1 对技术人员的建议
- **业务学习**：主动学习和理解业务流程
- **沟通方式**：使用业务人员能理解的语言进行沟通
- **需求理解**：深入理解业务需求的本质和目标

#### 5.3.2 对业务人员的建议
- **技术学习**：适当学习技术基础知识
- **需求表达**：清晰准确地表达业务需求
- **耐心沟通**：保持耐心进行多轮沟通

#### 5.3.3 协作机制建议
- **定期沟通**：建立定期沟通机制
- **文档规范**：建立标准化的需求文档规范
- **培训交流**：组织业务与技术的交流培训

## 6. 工作展望

### 6.1 智能体发展方向

#### 6.1.1 应用扩展
- **功能丰富**：从单一功能向综合功能扩展
- **场景覆盖**：覆盖更多业务场景和应用领域
- **智能提升**：提升智能体的分析和处理能力

#### 6.1.2 技术优化
- **性能提升**：优化运行速度和响应时间
- **准确性改进**：提高分析结果的准确性
- **用户体验**：改善用户使用体验

### 6.2 协作模式优化

#### 6.2.1 流程改进
- **需求管理**：优化需求管理流程
- **开发协作**：改进开发协作模式
- **测试验证**：完善测试验证机制

#### 6.2.2 能力建设
- **复合型人才**：培养既懂业务又懂技术的复合型人才
- **沟通能力**：提升跨部门沟通协作能力
- **创新思维**：培养创新思维和问题解决能力
