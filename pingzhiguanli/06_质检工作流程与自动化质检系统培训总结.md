# 质检工作流程与自动化质检系统培训总结

主要涵盖统一质检平台的使用、人工质检流程、自动质检系统以及数字员工管理等内容。

## 1. 统一质检平台概述

### 1.1 平台基本信息

#### 1.1.1 平台特点
- **全网统一**：采用全网统一的质检平台
- **节点完整**：包含质检工作所需的各类节点
- **专人对接**：数字团队有专门同事负责对接

#### 1.1.2 主要功能
- **录音质检**：对前台录音进行质量检查
- **结果处理**：质检结果的推送和处理
- **流程管理**：完整的质检工作流程管理

### 1.2 质检工作分类

#### 1.2.1 两大工作类型
1. **人工质检**：通过人工方式进行录音质检
2. **自动质检**：通过系统自动化进行质检

#### 1.2.2 工作重点
- **品质管理闭环**：确保质检结果得到有效处理
- **员工培训**：基于质检结果进行针对性培训
- **流程优化**：持续优化质检工作流程

## 2. 人工质检流程

### 2.1 数据筛选和质检操作

#### 2.1.1 数据筛选方式
- **模型筛选**：通过模型自动筛选质检数据
- **人工随机**：人工随机选择录音进行质检
- **数据调用**：调用相关质检录音数据

#### 2.1.2 质检模板使用
- **日常模板**：使用标准化的质检模板
- **问题评判**：评判员工是否存在服务问题
- **结果记录**：详细记录质检发现的问题

### 2.2 质检结果处理机制

#### 2.2.1 结果推送
- **推送对象**：质检结果推送给员工和班组长
- **查看权限**：班组长和员工都可以查看质检结果
- **及时通知**：通过系统及时通知相关人员

#### 2.2.2 处理流程
1. **无异议处理**：
   - 需要补救的进行补救操作
   - 需要培训的安排教练辅导
   - 推送到不满意度回访中心

2. **有异议处理**：
   - 员工或班组长可以提起申诉
   - 转给相应人员进行审核
   - 通过短信通知审核结果

### 2.3 补救和申诉机制

#### 2.3.1 补救流程
- **补救条件**：员工存在需要补救的问题
- **认领机制**：员工需要认领后才能进行补救
- **补救记录**：详细记录补救情况和效果

#### 2.3.2 申诉处理
- **申诉发起**：班组长和员工都可以发起申诉
- **在线处理**：通过在线系统进行申诉处理
- **审核流程**：相应处理人员收到工单进行审核

#### 2.3.3 业务类别管理
- **问题分类**：根据员工问题选择对应的业务类别
- **培训推送**：推送到智能教练进行针对性培训
- **课程生成**：根据质检差错生成对应的学习课程

## 3. 自动质检系统

### 3.1 智能模型质检

#### 3.1.1 语音智检模型
- **技术基础**：基于本部统一的语音质检模型
- **关键词设置**：通过录音文本关键词设置进行检测
- **录音捞取**：自动捞取相应的录音进行分析

#### 3.1.2 模型组应用
- **服务态度监控**：监控员工服务态度相关问题
- **服务质量检测**：检测服务质量类的投诉问题
- **全量监控**：对录音进行全量自动监控

#### 3.1.3 系统限制
- **离线服务器**：使用离线服务器进行处理
- **结果延时**：结果一般要到第二天才会出来
- **资源依赖**：依赖服务器空余时间和资源情况

### 3.2 RPA机器人质检

#### 3.2.1 通话中断监控
- **监控目标**：监控通话中断或员工意外挂机情况
- **回电检查**：检查员工是否及时回电给客户
- **服务提升**：避免客户重复服务，提升预警率

#### 3.2.2 工作流程
1. **模型规则**：通过模型规则捞取通话中断场景
2. **样本提取**：提取相关录音样本进行分析
3. **机器人判定**：通过RPA机器人进行自动判定
4. **接触记录核查**：通过接触记录查询客户呼叫情况

#### 3.2.3 判定标准
- **回电确认**：确认员工是否在中断后及时回电
- **合格标准**：已回电的情况按常规属于合格
- **自动化处理**：替代人工进行简单重复的数据判断

### 3.3 AI大模型质检

#### 3.3.1 灵运平台应用
- **技术基础**：基于AI大模型的灵云平台
- **功能优势**：在总结类和分析类功能方面较强大
- **效率提升**：快速归纳总结录音内容和客户问题

#### 3.3.2 应用场景
- **内容总结**：快速总结录音内容
- **问题归纳**：及时归纳客户问题
- **违规检测**：检测录音中的违规情况

#### 3.3.3 工作模式
- **辅助功能**：作为人工质检的辅助工具
- **结合分析**：结合文本内容进行综合判断
- **时间节省**：大幅节省复核录音的时间

## 4. 多渠道监控体系

### 4.1 监控渠道

#### 4.1.1 热线渠道
- **人工录音**：监控人工客服的录音质量
- **服务标准**：确保符合服务质量标准

#### 4.1.2 视频渠道
- **视频电话**：监控客户通过视频电话的服务质量
- **视频认证**：监控视频认证过程中的服务情况
- **多媒体服务**：覆盖多种服务渠道的质检需求

### 4.2 数据处理

#### 4.2.1 接触记录查询
- **手机号码查询**：通过手机号码查询相关记录
- **员工工号查询**：通过员工工号进行数据关联
- **数据导出**：导出对应模型的数据进行分析

#### 4.2.2 报表生成
- **自动生成**：通过接触记录自动生成报表
- **数据分析**：对质检数据进行统计分析
- **结果展示**：以报表形式展示质检结果

## 5. 数字员工管理

### 5.1 工号和权限管理

#### 5.1.1 申请流程
- **项目对应**：数字员工工号对应具体项目
- **权限申请**：需要申请对应的系统权限
- **审查机制**：纳入日常规范管理，审查较严格

#### 5.1.2 管理要求
- **规范化管理**：已纳入日常规范管理体系
- **严格审查**：对数字员工的使用进行严格审查
- **项目关联**：确保数字员工与具体项目的对应关系

### 5.2 应用场景

#### 5.2.1 退费质检
- **RPA应用**：退费质检的RPA机器人
- **生产职场**：部署在生产职场进行实际应用
- **自动化处理**：自动化处理退费相关的质检工作

#### 5.2.2 功能扩展
- **平台使用**：在各类平台中的应用
- **功能优化**：持续优化数字员工的功能
- **智能工具**：作为数据智能工具的重要组成

## 6. 团队协作机制

### 6.1 与数字团队的对接关系

#### 6.1.1 对接人员分工
- **张勇**：负责总体协调工作
- **晓明**：负责数据相关工作
- **陈思伟**：负责RPA机器人相关工作
- **梁超**：负责智能质检平台相关工作
- **戴维**：负责灵运平台的使用和开发

#### 6.1.2 协作内容
- **数据调取**：协助调取报表中没有的底层数据
- **功能反馈**：反馈平台使用中的问题和建议
- **需求开发**：协助开发新的报表和功能需求
- **技术支持**：提供各类技术支持和问题解决

### 6.2 沟通协作机制

#### 6.2.1 日常沟通
- **定期联系**：与不同负责人保持定期联系
- **问题反馈**：及时反馈使用中遇到的问题
- **需求提出**：根据业务需要提出功能需求

#### 6.2.2 协作范围
- **数据接口**：通过接口调取所需数据
- **底层数据**：获取底层数据支持
- **功能优化**：协助进行平台功能优化
- **技术改进**：推动技术方案的持续改进

## 7. 工作流程总结

### 7.1 质检工作的完整闭环

#### 7.1.1 质检执行
1. **数据筛选**：通过模型或人工方式筛选质检数据
2. **质检操作**：使用标准模板进行质检
3. **结果记录**：详细记录质检发现的问题

#### 7.1.2 结果处理
1. **结果推送**：推送给员工和班组长
2. **问题处理**：进行补救、培训或申诉
3. **效果跟踪**：跟踪处理效果和改进情况

### 7.2 自动化发展方向

#### 7.2.1 技术应用
- **多模型结合**：智能模型、RPA、AI大模型相结合
- **全渠道覆盖**：覆盖热线、视频等多个服务渠道
- **智能化提升**：持续提升自动化质检的智能化水平

#### 7.2.2 效率提升
- **人工辅助**：自动化作为人工质检的有效辅助
- **时间节省**：大幅节省质检工作时间
- **准确性提升**：提高质检结果的准确性和一致性
